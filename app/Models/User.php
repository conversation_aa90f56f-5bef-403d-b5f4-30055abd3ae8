<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Enums\UserRole;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Support\Str;

/**
 * @mixin IdeHelperUser
 *
 * @property int $id
 * @property string $name
 * @property string $email
 * @property \Illuminate\Support\Carbon|null $email_verified_at
 * @property string $password
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property UserRole $role
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Estate> $assignedEstates
 * @property-read int|null $assigned_estates_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
 * @property-read int|null $notifications_count
 *
 * @method static \Database\Factories\UserFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereEmailVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereRole($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|User whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'role' => \App\Enums\UserRole::class,
        ];
    }

    /**
     * Get the user's initials
     */
    public function initials(): string
    {
        return Str::of($this->name)
            ->explode(' ')
            ->take(2)
            ->map(fn ($word) => Str::substr($word, 0, 1))
            ->implode('');
    }

    /**
     * Check if user is admin staff
     */
    public function isAdmin(): bool
    {
        return $this->hasRole('admin');
    }

    /**
     * Check if user is management staff
     */
    public function isManager(): bool
    {
        return $this->hasRole('manager');
    }

    /**
     * Check if user is caretaker staff
     */
    public function isCaretaker(): bool
    {
        return $this->hasRole('caretaker');
    }

    /**
     * Check if user is reviewer staff
     */
    public function isReviewer(): bool
    {
        return $this->hasRole('reviewer');
    }

    /**
     * Check if user is resident
     */
    public function isResident(): bool
    {
        return $this->hasRole('resident');
    }

    /**
     * Get assigned estates for user
     */
    public function assignedEstates()
    {
        return $this->belongsToMany(Estate::class, 'user_estate_assignments')
            ->withPivot(['assigned_by', 'assigned_at'])
            ->withTimestamps();
    }

    /**
     * Get permission overrides for user
     */
    public function permissionOverrides()
    {
        return $this->belongsToMany(Permission::class, 'user_permission_overrides')
            ->withPivot(['action', 'granted_by', 'expires_at', 'reason'])
            ->withTimestamps();
    }

    /**
     * Get subordinates for manager
     */
    public function subordinates()
    {
        return $this->belongsToMany(User::class, 'user_management_hierarchy', 'manager_id', 'subordinate_id')
            ->withPivot(['relationship'])
            ->withTimestamps();
    }

    /**
     * Get managers for user
     */
    public function managers()
    {
        return $this->belongsToMany(User::class, 'user_management_hierarchy', 'subordinate_id', 'manager_id')
            ->withPivot(['relationship'])
            ->withTimestamps();
    }

    /**
     * Get assigned houses through assigned estates
     */
    public function assignedHouses()
    {
        return $this->hasManyThrough(
            House::class,
            Estate::class,
            'id', // Foreign key on estates table
            'estate_id', // Foreign key on houses table
            'id', // Local key on users table
            'id' // Local key on estates table
        )->whereIn('estates.id', function ($query) {
            $query->select('estate_id')
                ->from('user_estate_assignments')
                ->where('user_id', $this->id);
        });
    }

    /**
     * Check if user has a specific permission (with overrides)
     */
    public function hasPermission(string $permission): bool
    {
        // Check user overrides first
        if ($override = $this->permissionOverrides()->where('name', $permission)->first()) {
            if ($override->pivot->expires_at && $override->pivot->expires_at->isPast()) {
                $override->pivot->delete();
            } else {
                return $override->pivot->action === 'grant';
            }
        }

        // Check role-based permissions
        return in_array($permission, $this->role->permissions());
    }

    /**
     * Check if user can access a specific estate
     */
    public function canAccessEstate(Estate $estate): bool
    {
        if ($this->hasRole('admin')) {
            return true;
        }

        return $this->assignedEstates()->where('estates.id', $estate->id)->exists();
    }

    /**
     * Get contacts for resident users
     */
    public function contacts()
    {
        return $this->hasMany(Contact::class);
    }

    /**
     * Get house for resident users (through primary contact)
     */
    public function house()
    {
        return $this->hasOneThrough(
            House::class,
            Contact::class,
            'user_id', // Foreign key on contacts table
            'id', // Foreign key on houses table
            'id', // Local key on users table
            'house_id' // Local key on contacts table
        )->where('contacts.is_primary', true);
    }

    /**
     * Check if user can access a specific house
     */
    public function canAccessHouse(House $house): bool
    {
        if ($this->hasRole('admin')) {
            return true;
        }

        if ($this->hasRole('resident')) {
            return $this->contacts()->where('house_id', $house->id)->exists();
        }

        return $this->assignedEstates()->where('estates.id', $house->estate_id)->exists();
    }
}
